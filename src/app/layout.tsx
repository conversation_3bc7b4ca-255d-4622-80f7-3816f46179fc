import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON>, Dancing_Script } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/providers/session-provider";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { CaptivatingHeader } from "@/components/layout/CaptivatingHeader";
import { Footer } from "@/components/layout/footer";
import { Toaster } from "react-hot-toast";

const inter = Inter({ subsets: ["latin"] });
const dancingScript = Dancing_Script({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-dancing-script"
});

export const metadata: Metadata = {
  title: "PetAdopt - Find Your Perfect Companion",
  description: "Connect with loving pets in need of homes. Browse adoptable dogs, cats, and other animals from local shelters and rescue organizations.",
  keywords: "pet adoption, animal rescue, dogs, cats, shelter, adopt a pet",
  authors: [{ name: "PetAdopt Team" }],
  openGraph: {
    title: "PetAdopt - Find Your Perfect Companion",
    description: "Connect with loving pets in need of homes. Browse adoptable dogs, cats, and other animals from local shelters and rescue organizations.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "PetAdopt - Find Your Perfect Companion",
    description: "Connect with loving pets in need of homes. Browse adoptable dogs, cats, and other animals from local shelters and rescue organizations.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className} ${dancingScript.variable}`}>
        <ThemeProvider
          defaultTheme="system"
          storageKey="pet-adopt-theme"
        >
          <AuthProvider>
            <div className="min-h-screen flex flex-col bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
              <CaptivatingHeader />
              <main className="flex-1 pt-20">
                {children}
              </main>
              <Footer />
            </div>
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
              }}
            />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
