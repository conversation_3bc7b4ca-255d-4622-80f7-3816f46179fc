import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { getToken } from "next-auth/jwt"
import { UserRole, UserStatus } from "@prisma/client"

// Define protected routes and their required roles
const PROTECTED_ROUTES = {
  // Admin routes - require STAFF or ADMIN
  "/admin": [UserRole.STAFF, UserRole.ADMIN],
  "/api/admin": [UserRole.STAFF, UserRole.ADMIN],
  
  // User management - require ADMIN only
  "/admin/users": [UserRole.ADMIN],
  "/api/admin/users": [UserRole.ADMIN],
  
  // Reports and analytics - require STAFF or ADMIN
  "/admin/reports": [UserRole.STAFF, UserRole.ADMIN],
  "/api/admin/reports": [UserRole.STAFF, UserRole.ADMIN],
  
  // System settings - require ADMIN only
  "/admin/settings": [UserRole.ADMIN],
  "/api/admin/settings": [UserRole.ADMIN],
  
  // User dashboard and profile - require authentication
  "/dashboard": "authenticated",
  "/profile": "authenticated",
  "/applications": "authenticated",
  "/favorites": "authenticated",
  
  // API routes that require authentication
  "/api/user": "authenticated",
  "/api/applications": "authenticated",
  "/api/favorites": "authenticated",
} as const

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  "/",
  "/pets",
  "/about",
  "/contact",
  "/foster",
  "/volunteer",
  "/donate",
  "/blog",
  "/pet-care",
  "/track",
  "/adoption-stories",
  "/auth/signin",
  "/auth/signup",
  "/auth/forgot-password",
  "/auth/reset-password",
  "/api/auth",
  "/api/pets",
  "/api/public",
]

// Routes that should redirect authenticated users
const AUTH_ROUTES = [
  "/auth/signin",
  "/auth/signup",
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith("/_next") ||
    pathname.startsWith("/api/_next") ||
    pathname.includes(".") ||
    pathname.startsWith("/favicon")
  ) {
    return NextResponse.next()
  }

  try {
    // Get the user's token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })

    // Check if route is public
    const isPublicRoute = PUBLIC_ROUTES.some(route => 
      pathname === route || pathname.startsWith(route + "/")
    )

    // Check if route is an auth route
    const isAuthRoute = AUTH_ROUTES.some(route => 
      pathname === route || pathname.startsWith(route + "/")
    )

    // Redirect authenticated users away from auth pages
    if (isAuthRoute && token) {
      const redirectUrl = new URL("/dashboard", request.url)
      return NextResponse.redirect(redirectUrl)
    }

    // Allow access to public routes
    if (isPublicRoute) {
      return NextResponse.next()
    }

    // Check if route requires authentication or specific roles
    const routeRequirements = getRouteRequirements(pathname)
    
    if (!routeRequirements) {
      // Route not defined in protection rules, allow access
      return NextResponse.next()
    }

    // Check authentication
    if (!token) {
      const signInUrl = new URL("/auth/signin", request.url)
      signInUrl.searchParams.set("callbackUrl", pathname)
      return NextResponse.redirect(signInUrl)
    }

    // Check if user account is active
    if (token.status !== UserStatus.ACTIVE) {
      const errorUrl = new URL("/auth/account-inactive", request.url)
      return NextResponse.redirect(errorUrl)
    }

    // If route only requires authentication, allow access
    if (routeRequirements === "authenticated") {
      return NextResponse.next()
    }

    // Check role-based access
    if (Array.isArray(routeRequirements)) {
      const userRole = token.role as UserRole
      
      if (!routeRequirements.includes(userRole)) {
        // User doesn't have required role
        const accessDeniedUrl = new URL("/access-denied", request.url)
        return NextResponse.redirect(accessDeniedUrl)
      }
    }

    // Add security headers
    const response = NextResponse.next()
    
    // Add security headers
    response.headers.set("X-Frame-Options", "DENY")
    response.headers.set("X-Content-Type-Options", "nosniff")
    response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin")
    response.headers.set("Permissions-Policy", "camera=(), microphone=(), geolocation=()")
    
    // Add CSP header for admin routes
    if (pathname.startsWith("/admin")) {
      response.headers.set(
        "Content-Security-Policy",
        "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;"
      )
    }

    return response

  } catch (error) {
    console.error("Middleware error:", error)
    
    // In case of error, redirect to sign in for protected routes
    const routeRequirements = getRouteRequirements(pathname)
    if (routeRequirements) {
      const signInUrl = new URL("/auth/signin", request.url)
      signInUrl.searchParams.set("callbackUrl", pathname)
      return NextResponse.redirect(signInUrl)
    }
    
    return NextResponse.next()
  }
}

/**
 * Get route requirements for a given pathname
 */
function getRouteRequirements(pathname: string): UserRole[] | "authenticated" | null {
  // Check exact matches first
  if (pathname in PROTECTED_ROUTES) {
    return PROTECTED_ROUTES[pathname as keyof typeof PROTECTED_ROUTES]
  }

  // Check prefix matches
  for (const [route, requirements] of Object.entries(PROTECTED_ROUTES)) {
    if (pathname.startsWith(route + "/")) {
      return requirements
    }
  }

  return null
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    "/((?!_next/static|_next/image|favicon.ico|public/).*)",
  ],
}
